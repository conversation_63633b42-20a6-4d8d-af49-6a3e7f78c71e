# RuboCop configuration for brandrev-api project

require: rubocop-performance

AllCops:
  TargetRubyVersion: 3.1
  NewCops: enable
  SuggestExtensions: false
  Exclude:
    # Database files
    - 'db/schema.rb'
    - 'db/structure.sql'
    - 'db/migrate/*.rb'

    # Configuration files (YAML)
    - '**/*.yml'
    - '**/*.yaml'

    # Non-Ruby files
    - '**/*.md'
    - '**/*.txt'
    - '**/*.json'
    - '**/*.js'
    - '**/*.css'
    - '**/*.scss'
    - '**/*.html'
    - '**/*.erb'
    - '**/*.xml'
    - '**/*.sql'
    - 'Dockerfile*'
    - '**/*.toml'
    - '**/*.enc'

    # Generated/vendor files
    - 'vendor/**/*'
    - 'node_modules/**/*'
    - 'tmp/**/*'
    - 'log/**/*'
    - 'storage/**/*'
    - 'public/**/*'

    # Binary files and executables
    - 'bin/*'

    # Git and other VCS files
    - '.git/**/*'

# Common style preferences for Rails projects
Style/Documentation:
  Enabled: false

Style/FrozenStringLiteralComment:
  Enabled: true
  EnforcedStyle: always

Layout/LineLength:
  Max: 120
  AllowedPatterns: ['(\A|\s)#']

Metrics/BlockLength:
  Exclude:
    - 'spec/**/*'
    - 'config/routes.rb'
    - 'config/environments/*.rb'
    - 'db/migrate/*.rb'

Metrics/ClassLength:
  Max: 150