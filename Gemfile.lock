GEM
  remote: https://rubygems.org/
  specs:
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.2)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      nokogiri (>= 1.8.5)
      racc
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
      timeout (>= 0.4.0)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
    activesupport (*******)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.0.2)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      mutex_m
      securerandom (>= 0.3)
      tzinfo (~> 2.0)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    ast (2.4.3)
    aws-eventstream (1.3.0)
    aws-partitions (1.968.0)
    aws-sdk-core (3.201.5)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.651.0)
      aws-sigv4 (~> 1.9)
      jmespath (~> 1, >= 1.6.1)
    aws-sdk-kms (1.88.0)
      aws-sdk-core (~> 3, >= 3.201.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-s3 (1.159.0)
      aws-sdk-core (~> 3, >= 3.201.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.9.1)
      aws-eventstream (~> 1, >= 1.0.2)
    base64 (0.3.0)
    bcrypt (3.1.20)
    benchmark (0.4.1)
    bigdecimal (3.2.2)
    bootsnap (1.16.0)
      msgpack (~> 1.2)
    builder (3.3.0)
    childprocess (5.1.0)
      logger (~> 1.5)
    coderay (1.1.3)
    concurrent-ruby (1.3.5)
    connection_pool (2.4.1)
    crass (1.0.6)
    database_cleaner (2.1.0)
      database_cleaner-active_record (>= 2, < 3)
    database_cleaner-active_record (2.2.1)
      activerecord (>= 5.a)
      database_cleaner-core (~> 2.0.0)
    database_cleaner-core (2.0.1)
    date (3.4.1)
    debug (1.8.0)
      irb (>= 1.5.0)
      reline (>= 0.3.1)
    diff-lcs (1.5.1)
    discard (1.3.0)
      activerecord (>= 4.2, < 8)
    dockerfile-rails (1.5.0)
      rails
    domain_name (0.6.20240107)
    dotenv (3.1.2)
    drb (2.2.3)
    erubi (1.13.1)
    et-orbi (1.2.11)
      tzinfo
    event_stream_parser (1.0.0)
    faraday (2.7.9)
      faraday-net_http (>= 2.0, < 3.1)
      ruby2_keywords (>= 0.0.4)
    faraday-cookie_jar (0.0.7)
      faraday (>= 0.8.0)
      http-cookie (~> 1.0.0)
    faraday-encoding (0.0.6)
      faraday
    faraday-follow_redirects (0.3.0)
      faraday (>= 1, < 3)
    faraday-gzip (1.0.0)
      faraday (>= 1.0)
      zlib (~> 2.1)
    faraday-http-cache (2.5.1)
      faraday (>= 0.8)
    faraday-multipart (1.0.4)
      multipart-post (~> 2)
    faraday-net_http (3.0.2)
    faraday-retry (2.3.1)
      faraday (~> 2.0)
    fastimage (2.4.0)
    fugit (1.11.1)
      et-orbi (~> 1, >= 1.2.11)
      raabro (~> 1.4)
    globalid (1.2.1)
      activesupport (>= 6.1)
    honeybadger (4.12.2)
    http-accept (1.7.0)
    http-cookie (1.0.7)
      domain_name (~> 0.5)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    io-console (0.6.0)
    irb (1.7.0)
      reline (>= 0.3.0)
    jmespath (1.6.2)
    json (2.12.2)
    jwt (2.7.1)
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    language_server-protocol (********)
    launchy (3.0.1)
      addressable (~> 2.8)
      childprocess (~> 5.0)
    letter_opener (1.10.0)
      launchy (>= 2.2, < 4)
    lint_roller (1.1.0)
    logger (1.6.1)
    loofah (2.24.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    metainspector (5.15.0)
      addressable (~> 2.8.4)
      faraday (~> 2.5)
      faraday-cookie_jar (~> 0.0)
      faraday-encoding (~> 0.0)
      faraday-follow_redirects (~> 0.3)
      faraday-gzip (>= 0.1, < 2.0)
      faraday-http-cache (~> 2.5)
      faraday-retry (~> 2.0)
      fastimage (~> 2.2)
      nesty (~> 1.0)
      nokogiri (~> 1.13)
    method_source (1.1.0)
    mime-types (3.6.0)
      logger
      mime-types-data (~> 3.2015)
    mime-types-data (3.2024.1001)
    mini_mime (1.1.5)
    mini_portile2 (2.8.9)
    minitest (5.25.5)
    msgpack (1.7.1)
    multipart-post (2.4.1)
    mutex_m (0.3.0)
    nesty (1.0.2)
    net-imap (0.5.8)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.1)
      net-protocol
    netrc (0.11.0)
    nio4r (2.7.4)
    nokogiri (1.18.8)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    open-uri (0.2.0)
      stringio
      time
      uri
    parallel (1.27.0)
    parser (*******)
      ast (~> 2.4.1)
      racc
    pg (1.5.3)
    prism (1.4.0)
    pry (0.14.2)
      coderay (~> 1.1)
      method_source (~> 1.0)
    pry-rails (0.3.9)
      pry (>= 0.10.4)
    public_suffix (6.0.1)
    puma (6.6.0)
      nio4r (~> 2.0)
    raabro (1.4.0)
    racc (1.8.1)
    rack (3.1.16)
    rack-cors (2.0.1)
      rack (>= 2.0.0)
    rack-session (2.1.1)
      base64 (>= 0.1.0)
      rack (>= 3.0.0)
    rack-test (2.2.0)
      rack (>= 1.3)
    rackup (2.2.1)
      rack (>= 3)
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-dom-testing (2.3.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      irb
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.3.0)
    redis (5.0.6)
      redis-client (>= 0.9.0)
    redis-client (0.14.1)
      connection_pool
    regexp_parser (2.10.0)
    reline (0.3.5)
      io-console (~> 0.5)
    request_store (1.5.1)
      rack (>= 1.4)
    request_store-sidekiq (0.1.0)
      request_store (>= 1.3)
      sidekiq (>= 3.0)
    rest-client (2.1.0)
      http-accept (>= 1.7.0, < 2.0)
      http-cookie (>= 1.0.2, < 2.0)
      mime-types (>= 1.16, < 4.0)
      netrc (~> 0.8)
    rspec-core (3.13.1)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.5)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-json_expectations (2.2.0)
    rspec-mocks (3.13.1)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (7.0.1)
      actionpack (>= 7.0)
      activesupport (>= 7.0)
      railties (>= 7.0)
      rspec-core (~> 3.13)
      rspec-expectations (~> 3.13)
      rspec-mocks (~> 3.13)
      rspec-support (~> 3.13)
    rspec-support (3.13.1)
    rubocop (1.76.1)
      json (~> 2.3)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= *******)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.45.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.45.1)
      parser (>= *******)
      prism (~> 1.4)
    rubocop-performance (1.25.0)
      lint_roller (~> 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.38.0, < 2.0)
    ruby-openai (7.1.0)
      event_stream_parser (>= 0.3.0, < 2.0.0)
      faraday (>= 1)
      faraday-multipart (>= 1)
    ruby-progressbar (1.13.0)
    ruby2_keywords (0.0.5)
    securerandom (0.4.1)
    sentry-rails (4.8.1)
      railties (>= 5.0)
      sentry-ruby-core (~> 4.8.1)
    sentry-ruby (4.8.1)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      faraday (>= 1.0)
      sentry-ruby-core (= 4.8.1)
    sentry-ruby-core (4.8.1)
      concurrent-ruby
      faraday
    sentry-sidekiq (4.8.1)
      sentry-ruby-core (~> 4.8.1)
      sidekiq (>= 3.0)
    shoulda-matchers (6.5.0)
      activesupport (>= 5.2.0)
    sidekiq (7.1.2)
      concurrent-ruby (< 2)
      connection_pool (>= 2.3.0)
      rack (>= 2.2.4)
      redis-client (>= 0.14.0)
    sidekiq-cron (1.12.0)
      fugit (~> 1.8)
      globalid (>= 1.0.1)
      sidekiq (>= 6)
    stringio (3.1.1)
    thor (1.3.2)
    tiktoken_ruby (0.0.5-aarch64-linux)
    tiktoken_ruby (0.0.5-arm64-darwin)
    tiktoken_ruby (0.0.5-x86_64-linux)
    time (0.2.0)
      date
    timeout (0.4.3)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode-display_width (3.1.4)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    uri (0.13.0)
    websocket-driver (0.8.0)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    zeitwerk (2.6.18)
    zlib (2.1.1)

PLATFORMS
  aarch64-linux
  arm64-darwin-22
  arm64-darwin-24
  x86_64-linux

DEPENDENCIES
  aws-sdk-s3
  bcrypt (~> 3.1.7)
  bootsnap
  database_cleaner
  debug
  discard (~> 1.2)
  dockerfile-rails (>= 1.5)
  dotenv
  faraday
  honeybadger (~> 4.0)
  jwt
  kaminari
  letter_opener
  metainspector
  open-uri
  pg (~> 1.1)
  pry-rails
  puma (~> 6.5)
  rack-cors
  rails (~> *******)
  redis (~> 5.0)
  request_store (~> 1.5)
  request_store-sidekiq (= 0.1.0)
  rest-client
  rspec-json_expectations (~> 2.2)
  rspec-rails (~> 7.0.0)
  rubocop
  rubocop-performance
  ruby-openai (= 7.1.0)
  sentry-rails (~> 4.8, >= 4.8.1)
  sentry-ruby (~> 4.8, >= 4.8.1)
  sentry-sidekiq (~> 4.8, >= 4.8.1)
  shoulda-matchers
  sidekiq
  sidekiq-cron
  tiktoken_ruby
  tzinfo-data

RUBY VERSION
   ruby 3.1.2p20

BUNDLED WITH
   2.3.7
