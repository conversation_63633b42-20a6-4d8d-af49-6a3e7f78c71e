# frozen_string_literal: true

class Membership < ApplicationRecord
  include Discard::Model
  default_scope -> { kept }

  belongs_to :user
  belongs_to :organization
  belongs_to :organization_team, optional: true

  has_many :workspaces_memberships

  def self.list_membership_roles
    {
      -5 => 'owner_platform',
      -4 => 'super_admin_platform',
      -3 => 'admin',
      -2 => 'super_user',
      -1 => 'partner_admin',
      0 => 'owner',
      1 => 'super_admin',
      2 => 'team_admin',
      3 => 'member'
    }
  end

  def self.role_mappings
    {
      'owner_platform' => -5,
      'super_admin_platform' => -4,
      'admin' => -3,
      'super_user' => -2,
      'partner_admin' => -1,
      'owner' => 0,
      'super_admin' => 1,
      'team_admin' => 2,
      'member' => 3

    }
  end

  def membership_role
    self.class.list_membership_roles[role] || 'member'
  end
end
