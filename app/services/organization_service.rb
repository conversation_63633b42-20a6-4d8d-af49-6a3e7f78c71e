# frozen_string_literal: true

class OrganizationService < AppService
  def create(params)
    authorize_user_roles!(@user, %w[super_user admin super_admin super_admin_platform owner_platform])

    monthly_credits_refresh = params.delete(:monthly_credits_refresh).to_i
    max_members = params.delete(:max_members).to_i
    starting_credits = params.delete(:starting_credits).to_i
    designated_owner_email = params.delete(:designated_owner_email)&.strip&.downcase

    params[:code] = params[:name].parameterize if params[:code].blank?
    params[:created_by_id] = @user.id

    refresh_date = params.delete(:refresh_date) || Time.current.day
    assert! refresh_date.to_i >= 0 && refresh_date.to_i <= 31, on_error: 'Refresh date invalid'

    organization = Organization.new
    plan_threshold = OrganizationsPlansThreshold.new
    ActiveRecord::Base.transaction do
      current_time = Time.current

      organization = Organization.create!(params)

      plan_threshold = OrganizationsPlansThreshold.create!(
        organization_id: organization.id,
        monthly_credits_refresh: monthly_credits_refresh,
        max_members: max_members,
        max_workspaces: 1,
        purchased_credits: starting_credits,
        refresh_date: refresh_date
      )

      CreditHistory.create!(
        organization_id: organization.id,
        action: 'su_create_org',
        monthly_credits: 0,
        purchased_credits: starting_credits,
        action_at: current_time,
        user_id: @user.id
      )

      if designated_owner_email.present?
        user = User.find_by(email: designated_owner_email)
        # TODO: Need more scope
        # Solution from Brandrev
        user ||= User.create!(email: designated_owner_email, password: "#{organization.id}1234xzq3",
                              display_name: designated_owner_email)

        # TODO: if present it will override current membership
        membership = Membership.find_or_initialize_by(user_id: user.id)
        membership.role = Membership.role_mappings['owner']
        membership.organization_id = organization.id
        membership.save!

        Mailer::OnboardOrganizationMailerJob.perform_later(membership)
      end
    end

    OpenStruct.new(
      organization: organization,
      organizations_plans_thresholds: [plan_threshold]
    )
  end

  def update(id, params)
    authorize_user_roles!(@user, %w[super_user owner super_admin admin super_admin_platform owner_platform])

    organization = Organization.find(id)
    authorize! organization_ownership(organization), on_error: 'Not owned organization!'

    plan_threshold = OrganizationsPlansThreshold.find_by(organization_id: organization.id)

    if params[:code].present? && params[:code] != organization.code
      assert! !params[:code].include?(' '), on_error: 'Code cannot contain whitespace'
    else
      params[:code] = organization.code
    end

    refresh_date = params.delete(:refresh_date)
    assert! refresh_date.nil? || (refresh_date.to_i >= 0 && refresh_date.to_i <= 31), on_error: 'Refresh date invalid'

    update_plan_params = {
      monthly_credits_refresh: params.delete(:monthly_credits_refresh),
      max_members: params.delete(:max_members),
      refresh_date: refresh_date
    }.compact

    add_credits = params.delete(:add_credits)
    update_plan_params[:purchased_credits] = plan_threshold.purchased_credits + add_credits.to_i if add_credits.present?

    ActiveRecord::Base.transaction do
      current_time = Time.current

      organization.update!(params)

      if %w[super_user admin super_admin super_admin_platform owner_platform].include?(@user.membership.membership_role)
        plan_threshold&.update(update_plan_params)

        if add_credits.present?
          CreditHistory.create!(
            organization_id: organization.id,
            action: 'su_add_credits',
            monthly_credits: 0,
            purchased_credits: add_credits,
            action_at: current_time,
            user_id: @user.id
          )
        end
      end
    end

    OpenStruct.new(
      organization: organization,
      organizations_plans_thresholds: [plan_threshold]
    )
  end

  def show(id)
    authorize_user_roles!(@user, %w[super_user owner super_admin admin super_admin_platform owner_platform])

    organization = Organization.find(id)

    authorize! organization_ownership(organization), on_error: 'Not owned organization!'

    plan_threshold = OrganizationsPlansThreshold.find_by(organization_id: organization.id)

    OpenStruct.new(
      organization: organization,
      organizations_plans_thresholds: [plan_threshold]
    )
  end

  def index(query_params)
    authorize_user_roles!(@user, %w[super_user admin super_admin super_admin_platform owner_platform])

    organizations = ::Organizations.new

    filter = query_params.slice(:search, :page, :per_page)

    # Allow admin, super_admin_platform and owner_platform to filter by organization_id
    if (platform_admin? || @user.role == 'partner_admin') && query_params[:organization_id].present?
      filter[:organization_id] = query_params[:organization_id]
    end

    filter[:created_by_id] = @user.id if @user.role == 'partner_admin'

    filter = filter.merge(
      disable_pagination: true
    )

    filtered = organizations.filter(filter)
    org_ids = filtered.pluck(:id)

    OpenStruct.new(
      organizations: filtered,
      organizations_plans_thresholds: OrganizationsPlansThreshold.where(organization_id: org_ids),
      owner_memberships: Membership.includes(:user).where(organization_id: org_ids,
                                                          role: Membership.role_mappings['owner']).order('users.email')
    )
  end

  def transfer_ownership(id, params)
    membership = verify_user_organization(@user)
    authorize_user_roles!(@user, %w[super_user owner admin super_admin super_admin_platform owner_platform])

    if Membership.role_mappings['owner'] == membership.role
      authorize! id.to_i == membership.organization_id, on_error: 'Not owned organization!'
    end

    email = params.delete(:designated_owner_email)&.strip&.downcase

    ActiveRecord::Base.transaction do
      Membership.where(organization_id: id, role: Membership.role_mappings['owner'])
                .update_all(role: Membership.role_mappings['super_admin'])

      if email.present?
        new_user = User.find_by(email: email)

        if new_user.present?
          new_membership = Membership.unscoped.find_by(user_id: new_user.id)
          new_membership.update(
            role: Membership.role_mappings['owner'],
            organization_id: id,
            discarded_at: nil,
            organization_team_ids: []
          )

          Mailer::OnboardOrganizationMailerJob.perform_later(new_membership)
        else
          new_user = User.create!(email: email, password: "#{id}1234xzq3", display_name: email)

          new_membership = Membership.create!(
            role: Membership.role_mappings['owner'],
            organization_id: id,
            user_id: new_user.id
          )

          Mailer::OnboardOrganizationMailerJob.perform_later(new_membership)
        end
      end
    end
  end

  def get_remaining_tokens(id)
    authorize_user_roles!(@user, %w[admin super_admin_platform owner_platform])

    organization = Organization.find(id)
    authorize! organization_ownership(organization), on_error: 'Not owned organization!'

    plan_threshold = OrganizationsPlansThreshold.find_by(organization_id: organization.id)

    OpenStruct.new(
      id: organization.id,
      name: organization.name,
      purchased_credits: plan_threshold&.purchased_credits.to_f,
      remaining_monthly_credits: plan_threshold&.remaining_monthly_credits.to_f,
      monthly_credits_refresh: plan_threshold&.monthly_credits_refresh,
      refresh_date: plan_threshold&.refresh_date
    )
  end
end
