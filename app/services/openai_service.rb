# frozen_string_literal: true

require 'tiktoken_ruby'

class OpenaiService < BaseLlmService
  GPT4O_TOKEN_CONVERSION_RATE_INPUT = 1
  GPT4O_TOKEN_CONVERSION_RATE_OUTPUT = 3
  GPT4OMINI_TOKEN_CONVERSION_RATE_INPUT = 0.03
  GPT4OMINI_TOKEN_CONVERSION_RATE_OUTPUT = 0.12

  def initialize(user)
    @user = user
    @client = initialize_client
  end

  def stream_response_v2(sse, params)
    chat = params.chat
    model = params.model
    user_messages = params.user_messages
    assistant_message_obj = params.assistant_message_obj
    user_message_obj = params.user_message_obj
    openai_assistant_id = model.openai_assistant_id

    openai_chat = OpenaiChat.find_by(chat_id: chat.id)

    if openai_chat
      assistant_id = openai_assistant_id || openai_chat.openai_assistant_id
      thread_id = openai_chat.openai_thread_id

      if thread_id.nil?
        thread_id = @client.threads.create['id']

        openai_chat.update(
          openai_thread_id: thread_id
        )
      end

      @client.messages.create(
        thread_id: thread_id,
        parameters: user_messages
      )

      @client.runs.create(
        thread_id: thread_id,
        parameters: {
          assistant_id: assistant_id,
          stream: proc do |chunk, _bytesize|
            if chunk['object'] == 'thread.message.delta'
              print chunk.dig('delta', 'content', 0, 'text', 'value')
              assistant_message_obj.content = assistant_message_obj.content + chunk.dig('delta', 'content', 0, 'text',
                                                                                        'value').to_s
              sse.write(chunk.merge(chat_id: chat.id))
            end

            if chunk['object'] == 'thread.run' && chunk['status'] == 'completed'
              chunk['id']
              usage_data = chunk['usage']
              print("\n", usage_data, "\n")

              user_message_obj.tokens_used = usage_data['prompt_tokens']
              user_message_obj.credits_used = token_to_credit(model.model, usage_data['prompt_tokens'],
                                                              user_message_obj.sender)
              assistant_message_obj.tokens_used = usage_data['completion_tokens']
              assistant_message_obj.credits_used = token_to_credit(model.model, usage_data['completion_tokens'],
                                                                   assistant_message_obj.sender)
            end
          end
        }
      )

      user_message_obj.save!
      assistant_message_obj.save!
      sse.write('[DONE]')
    else
      if openai_assistant_id
        assistant_id = openai_assistant_id
      else
        assistant_id = create_assistant(model)['id']

        model.update(openai_assistant_id: assistant_id)
      end

      thread_id = @client.threads.create['id']

      @client.messages.create(
        thread_id: thread_id,
        parameters: user_messages
      )

      @client.runs.create(
        thread_id: thread_id,
        parameters: {
          assistant_id: assistant_id,
          stream: proc do |chunk, _bytesize|
            if chunk['object'] == 'thread.message.delta'
              print chunk.dig('delta', 'content', 0, 'text', 'value')
              assistant_message_obj.content = assistant_message_obj.content + chunk.dig('delta', 'content', 0, 'text',
                                                                                        'value').to_s
              sse.write(chunk.merge(chat_id: chat.id))
            end

            if chunk['object'] == 'thread.run' && chunk['status'] == 'completed'
              chunk['id']
              usage_data = chunk['usage']
              print("\n", usage_data, "\n")

              user_message_obj.tokens_used = usage_data['prompt_tokens']
              user_message_obj.credits_used = token_to_credit(model.model, usage_data['prompt_tokens'],
                                                              user_message_obj.sender)
              assistant_message_obj.tokens_used = usage_data['completion_tokens']
              assistant_message_obj.credits_used = token_to_credit(model.model, usage_data['completion_tokens'],
                                                                   assistant_message_obj.sender)
            end
          end
        }
      )

      OpenaiChat.create(
        chat_id: chat.id,
        openai_thread_id: thread_id,
        openai_assistant_id: assistant_id
      )

      user_message_obj.save!
      assistant_message_obj.save!
      sse.write('[DONE]')
    end
  end

  def create_assistant(model, **options)
    tools = assistant_tools_builder(model)

    parameters = {
      model: model.model,
      name: "#{model.model_template_id}: #{model.name}",
      temperature: model.temperature,
      description: '',
      instructions: model.instruction,
      tools: tools
    }

    parameters[:tool_resources] = options[:tool_resources] if options[:tool_resources].present?

    @client.assistants.create(parameters: parameters)
  end

  def modify_assistant(model, **options)
    tools = assistant_tools_builder(model)
    assistant_id = model.openai_assistant_id

    parameters = {
      model: model.model,
      name: "#{model.model_template_id}: #{model.name}",
      temperature: model.temperature,
      description: '',
      instructions: model.instruction,
      tools: tools
    }

    parameters[:tool_resources] = options[:tool_resources] if options[:tool_resources].present?

    @client.assistants.modify(
      id: assistant_id,
      parameters: parameters
    )
  end

  def retrieve_assistant(assistant_id)
    @client.assistants.retrieve(id: assistant_id)
  end

  def delete_assistant(assistant_id)
    @client.assistants.delete(id: assistant_id)
  end

  def create_assistant_files(vector_store_id, **options)
    mode = options[:mode]

    return unless vector_store_id.present? && %w[openai_file_ids file_urls].include?(mode)

    openai_file_ids = []
    purpose = 'assistants'

    if mode == 'file_urls'
      file_urls = options[:file_urls]
      model = options[:model]
      file_urls.each do |url|
        response_file = create_file(url, purpose)
        openai_file_ids << response_file['id']

        OpenaiFile.create!(
          object_id: model.id,
          object_class: model.class.name,
          object_class_column: 'openai_assistant_id',
          openai_file_id: response_file['id']
        )
      end
    elsif mode == 'openai_file_ids'
      openai_file_ids = options[:openai_file_ids]
    end

    @client.vector_store_file_batches.create(
      vector_store_id: vector_store_id,
      parameters: {
        file_ids: openai_file_ids
      }
    )
  end

  def create_vector_store(name)
    @client.vector_stores.create(
      parameters: {
        name: name
      }
    )
  end

  def delete_vector_store(vector_store_id)
    response_vector_store_files = list_vector_store_files(vector_store_id)
    openai_file_ids = response_vector_store_files['data'].collect { |file| file['id'] }

    openai_file_ids.each do |f_id|
      delete_file(f_id)
    end

    @client.vector_stores.delete(id: vector_store_id)
  end

  def list_vector_store_files(vector_store_id)
    @client.vector_store_files.list(vector_store_id: vector_store_id)
  end

  def create_file(file_url, purpose)
    filename = File.basename(URI.parse(file_url).path)
    root_filepath = Rails.root.join('tmp', filename)
    File.binwrite(root_filepath, URI.open(file_url).read)

    open_file = File.open(root_filepath)

    response = @client.files.upload(parameters: { file: open_file, purpose: purpose })

    File.delete(root_filepath)

    response
  end

  def handle_file_search(file_url, user_prompt, message_data, user_messages)
    message_file = create_file(file_url, 'assistants')
    attachments = [
      { file_id: message_file['id'], tools: [{ type: 'file_search' }] }
    ]

    user_messages.merge!(
      role: 'user',
      content: user_prompt,
      attachments: attachments
    )

    message_data[:content] = user_prompt
    message_data[:file_url] = file_url
    message_data[:openai_file_ids] = [message_file['id']]
  end

  def delete_file(openai_file_id)
    @client.files.delete(id: openai_file_id)
  end

  private

  def initialize_client
    OpenAI::Client.new(
      access_token: Rails.application.credentials.openai_key
    )
  end

  def assistant_tools_builder(model)
    tools = []

    tools << { type: 'file_search' } if model.file_search

    tools << { type: 'code_interpreter' } if model.code_interpreter

    tools
  end

  def token_to_credit(model, tokens, sender)
    used_conversion = ''

    if model == 'gpt-4o'
      used_conversion += 'OpenaiService::GPT4O_TOKEN_CONVERSION_RATE'
    elsif model == 'gpt-4o-mini'
      used_conversion += 'OpenaiService::GPT4OMINI_TOKEN_CONVERSION_RATE'
    end

    if sender == 'user'
      used_conversion += '_INPUT'
    elsif sender == 'assistant'
      used_conversion += '_OUTPUT'
    end

    rates = used_conversion.constantize
    (tokens * rates).ceil(9)
  end
end
