# frozen_string_literal: true

module V1
  class OrganizationOutput < ApiOutput
    def format
      {
        id: @object.id,
        name: @object.name,
        logo_url: @object.logo_url,
        organization_plan_threshold: organization_plan_threshold_output,
        owner: owner_output,
        code: @object.code,
        created_by_id: @object.created_by_id
      }
    end

    def organization_plan_threshold_output
      curr_plan = organizations_plans_thresholds.find { |p| p&.organization_id == @object.id }

      return unless curr_plan.present?

      {
        purchased_credits: curr_plan.purchased_credits.to_f,
        remaining_monthly_credits: curr_plan.remaining_monthly_credits.to_f,
        monthly_credits_refresh: curr_plan.monthly_credits_refresh,
        refresh_date: curr_plan.refresh_date,
        max_members: curr_plan.max_members
      }
    end

    def owner_output
      return if owner_memberships.blank?

      curr_owner_membership = owner_memberships.find { |m| m.organization_id == @object.id }

      return unless curr_owner_membership.present?

      {
        id: curr_owner_membership.user_id,
        display_name: curr_owner_membership.user&.display_name,
        photo_url: curr_owner_membership.user&.photo_url,
        email: curr_owner_membership.user&.email
      }
    end

    def organizations_plans_thresholds
      @options[:organizations_plans_thresholds] || []
    end

    def owner_memberships
      @options[:owner_memberships]
    end
  end
end
