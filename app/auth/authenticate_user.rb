# frozen_string_literal: true

class AuthenticateUser
  attr_reader :auth_user, :auth_org

  def initialize(email, password, code, auth_user = nil, _referer = nil, auth_org = nil)
    @email = email
    @password = password
    @auth_user = auth_user
    @auth_org = auth_org
    @code = code
  end

  def call
    return unless user
    return unless organization

    membership_role = verify_membership!

    payload = { user_id: user.id, role: membership_role }

    # because weird error in webapp
    # create token that practically last forever
    @auth_user = user
    @auth_org = organization
    JsonWebToken.encode(payload, 10.years)
  end

  private

  attr_reader :email, :password, :code

  def user
    user = User.find_by(email:)

    raise ExceptionHandler::AuthenticationError, ErrorMessage.invalid_credentials unless user
    return user if user.authenticate(password)

    raise(ExceptionHandler::AuthenticationError, ErrorMessage.invalid_credentials)
  end

  def organization
    organization = Organization.find_by(code:)
    raise ExceptionHandler::AuthenticationError, ErrorMessage.invalid_credentials unless organization

    organization
  end

  def verify_membership!
    return user.role if user.platform_admin?

    if user.role == 'partner_admin'
      user_id = user.id.is_a?(Integer) ? user.id.to_s : user.id
      return user.role if organization.created_by_id == user_id
    end

    membership = Membership.find_by(user_id: user.id, organization_id: organization.id)
    return membership.membership_role if membership.present?

    raise ExceptionHandler::AuthenticationError, ErrorMessage.invalid_credentials
  end
end
