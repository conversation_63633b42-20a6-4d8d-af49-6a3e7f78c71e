# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::Organizations#index', type: :request do
  let!(:admin) do
    User.create!(
      display_name: 'Test',
      email: '<EMAIL>',
      password: '12345678'
    )
  end

  let!(:member) do
    User.create!(
      display_name: 'Member',
      email: '<EMAIL>',
      password: '12345678'
    )
  end

  let!(:partner_admin) do
    User.create!(
      display_name: 'Partner Admin',
      email: '<EMAIL>',
      password: '12345678'
    )
  end

  let!(:main_org) do
    Organization.create!(name: 'BrandRev', code: 'brandrev')
  end

  let!(:first_org) do
    Organization.create!(name: 'First Org', code: 'first-org')
  end

  let!(:second_org) do
    Organization.create!(name: 'Second Org', code: 'second-org')
  end

  let!(:third_org) do
    Organization.create!(name: 'Third Org', code: 'third-org')
  end

  let!(:membership_admin) do
    Membership.create!(
      user_id: admin.id,
      organization_id: main_org.id,
      role: -3
    )
  end

  let!(:membership_member) do
    Membership.create!(
      user_id: member.id,
      organization_id: first_org.id,
      role: 0
    )
  end

  let!(:membership_partner_admin) do
    Membership.create!(
      user_id: partner_admin.id,
      organization_id: first_org.id,
      role: -1
    )
  end

  let!(:organization_plan_thresholds) do
    OrganizationsPlansThreshold.create!(
      organization_id: first_org.id,
      monthly_credits_refresh: 100,
      max_members: 10,
      max_workspaces: 10,
      purchased_credits: 100,
      refresh_date: 1
    )
  end

  def index_organizations(params, user)
    get '/v1/organizations', params, as_user(user)
  end

  describe 'GET list organizations' do
    context 'when user is partner admin' do
      it 'return list organizations created by user' do
        second_org.update!(created_by_id: partner_admin.id)
        index_organizations({}, partner_admin)
        expect_response(:ok)

        expect(response_data.size).to eq 1
      end

      it 'return list filtered by organization_id and created by user' do
        second_org.update!(created_by_id: partner_admin.id)
        first_org.update!(created_by_id: partner_admin.id)
        index_organizations({ organization_id: second_org.id }, partner_admin)
        expect_response(:ok)

        expect(response_data.size).to eq 1
      end

      it 'return empty list when no created org' do
        index_organizations({}, partner_admin)
        expect_response(:ok)

        expect(response_data.size).to eq 0
      end
    end

    it 'return error when user is not admin' do
      index_organizations({}, member)
      expect_response(:forbidden)
    end

    it 'return list organizations for admin' do
      index_organizations({}, admin)
      expect_response(:ok)

      expect(response_data.size).to eq 4
    end

    it 'return list filtered by organization_id for admin' do
      index_organizations({ organization_id: first_org.id }, admin)
      expect_response(:ok)

      expect(response_data.size).to eq 1
    end
  end
end
