# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::Organizations#create', type: :request do
  let!(:admin) do
    User.create!(
      display_name: 'Test',
      email: '<EMAIL>',
      password: '12345678'
    )
  end

  let!(:member) do
    User.create!(
      display_name: 'Member',
      email: '<EMAIL>',
      password: '12345678'
    )
  end

  let!(:organization) do
    Organization.create!(name: 'ORG Main')
  end

  let!(:membership_admin) do
    Membership.create!(
      user_id: admin.id,
      organization_id: organization.id,
      role: 1
    )
  end

  let(:valid_params) do
    {
      name: 'Test',
      logo_url: 'https://www.google.com',
      monthly_credits_refresh: 1,
      max_members: 1,
      starting_credits: 1,
      refresh_date: 1,
      designated_owner_email: '<EMAIL>',
      code: 'test'
    }
  end

  def create(params, which_user = admin)
    post '/v1/organizations', params, as_user(which_user)
  end

  describe 'POST create organization' do
    context 'when invalid params' do
      let(:invalid_params) do
        valid_params.except(:name)
      end

      it 'return error' do
        create(invalid_params, admin)
        expect_response(:unprocessable_entity)
      end
    end

    it 'create organization with code' do
      create(valid_params, admin)
      expect_response(:created)

      expect(response_data['code']).to eq 'test'
    end

    it 'create organization with parameterized name code' do
      params = valid_params.merge(name: 'Test 1').except(:code)
      create(params, admin)
      expect_response(:created)

      expect(response_data['code']).to eq 'test-1'

      params = valid_params.merge(name: 'Test 2', code: '')
      create(params, admin)
      expect_response(:created)

      expect(response_data['code']).to eq 'test-2'
    end

    it 'create organization and user when designated owner email is not registered' do
      valid_params[:designated_owner_email] = '<EMAIL>'
      create(valid_params, admin)
      expect_response(:created)

      created_user = User.find_by(email: '<EMAIL>')
      expect(created_user.present?).to be_truthy

      created_membership = Membership.find_by(user_id: created_user.id)
      expect(created_membership.present?).to be_truthy
      expect(created_membership.role).to eq Membership.role_mappings['owner']
    end

    it 'create organization with created_by_id' do
      create(valid_params, admin)
      expect_response(:created)

      expect(response_data['created_by_id']).to eq admin.id.to_s

      organization = Organization.find(response_data['id'])
      expect(organization.created_by_id).to eq admin.id.to_s
    end
  end
end
