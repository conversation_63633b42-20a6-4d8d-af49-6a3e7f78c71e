# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::Organizations#update', type: :request do
  let!(:admin) do
    User.create!(
      display_name: 'Test',
      email: '<EMAIL>',
      password: '12345678'
    )
  end

  let!(:member) do
    User.create!(
      display_name: 'Member',
      email: '<EMAIL>',
      password: '12345678'
    )
  end

  let!(:organization) do
    Organization.create!(name: 'ORG Main', code: 'org-main')
  end

  let!(:membership_admin) do
    Membership.create!(
      user_id: admin.id,
      organization_id: organization.id,
      role: 1
    )
  end

  let!(:membership_member) do
    Membership.create!(
      user_id: member.id,
      organization_id: organization.id,
      role: 3
    )
  end

  let(:valid_params) do
    {
      name: 'Test',
      logo_url: 'https://www.google.com',
      monthly_credits_refresh: 1,
      max_members: 1,
      starting_credits: 1,
      refresh_date: 1,
      code: 'test'
    }
  end

  def update(id, params, which_user = admin)
    patch "/v1/organizations/#{id}", params, as_user(which_user)
  end

  describe 'PATCH update organization' do
    it 'update organization new attributes' do
      update(organization.id, valid_params, admin)
      expect_response(:ok)

      expect(response_data['name']).to eq 'Test'
      expect(response_data['logo_url']).to eq 'https://www.google.com'
      expect(response_data['code']).to eq 'test'

      organization.reload
      expect(organization.name).to eq 'Test'
      expect(organization.logo_url).to eq 'https://www.google.com'
      expect(organization.code).to eq 'test'
    end

    it 'update organization code if params empty to current code' do
      update(organization.id, valid_params.merge(code: nil), admin)
      expect_response(:ok)

      expect(response_data['code']).to eq 'org-main'

      organization.reload
      expect(organization.code).to eq 'org-main'
    end
  end
end
